package com.synqnc.opengw.operate.service.bill.service.itf;

import com.synqnc.opengw.common.utils.PageUtils;
import com.synqnc.opengw.dataservice.bill.modelvo.BillDataListQueryVO;
import com.synqnc.opengw.dataservice.bill.modelvo.BillDataListVO;
import com.synqnc.opengw.dataservice.uniauth.modelvo.LoginUserInfo;

/**
 * BillDataListService
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2025/8/6
 */
public interface BillDataListService {

    /**
     * 查询话单列表
     *
     * @param queryVO 查询条件
     * @param loginUserInfo 登录用户信息
     * @return 话单列表
     */
    PageUtils<BillDataListVO> getBillDataList(BillDataListQueryVO queryVO, LoginUserInfo loginUserInfo);
}
