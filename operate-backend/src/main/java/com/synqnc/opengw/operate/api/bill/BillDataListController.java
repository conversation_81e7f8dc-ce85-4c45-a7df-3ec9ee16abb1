package com.synqnc.opengw.operate.api.bill;

import com.synqnc.opengw.common.service.uniauth.service.itf.LoginUserService;
import com.synqnc.opengw.common.utils.PageUtils;
import com.synqnc.opengw.dataservice.bill.modelvo.BillDataListQueryVO;
import com.synqnc.opengw.dataservice.bill.modelvo.BillDataListVO;
import com.synqnc.opengw.dataservice.common.modelui.ControllerReturnUI;
import com.synqnc.opengw.dataservice.uniauth.modelvo.LoginUserInfo;
import com.synqnc.opengw.operate.service.bill.service.itf.BillDataListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * BillDataListController
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2025/8/6
 */
@Api(value = "话单列表", tags = {"话单列表"})
@RestController
@RequestMapping("/bill")
@Slf4j
public class BillDataListController {

    @Autowired
    private BillDataListService billDataListService;

    @Autowired
    private LoginUserService loginUserService;

    /**
     * 查询话单列表
     *
     * @param queryVO 查询条件
     * @param httpRequest HTTP请求
     * @return 话单列表
     */
    @ApiOperation(value = "查询话单列表", notes = "根据条件查询话单列表，按话单生成时间倒序")
    @PostMapping("/getBillDataList")
    public ControllerReturnUI<PageUtils<BillDataListVO>> getBillDataList(
            @RequestBody BillDataListQueryVO queryVO, 
            HttpServletRequest httpRequest) {
        
        log.info("BillDataListController.getBillDataList enter, queryVO={}", queryVO);
        
        try {
            // 获取登录用户信息
            LoginUserInfo loginUserInfo = loginUserService.getLoginUserInfo(httpRequest);
            
            // 调用服务层
            PageUtils<BillDataListVO> result = billDataListService.getBillDataList(queryVO, loginUserInfo);
            
            log.info("BillDataListController.getBillDataList exit, totalCount={}", result.getTotalCount());
            
            return ControllerReturnUI.generateByData(result);
            
        } catch (Exception e) {
            log.error("BillDataListController.getBillDataList error, queryVO={}", queryVO, e);
            throw new RuntimeException("查询话单列表失败：" + e.getMessage());
        }
    }
}
