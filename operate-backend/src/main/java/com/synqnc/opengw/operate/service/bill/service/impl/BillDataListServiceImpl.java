package com.synqnc.opengw.operate.service.bill.service.impl;

import com.alibaba.fastjson.JSON;
import com.synqnc.opengw.common.utils.PageUtils;
import com.synqnc.opengw.dataservice.bill.modelvo.BillDataListQueryVO;
import com.synqnc.opengw.dataservice.bill.modelvo.BillDataListVO;
import com.synqnc.opengw.dataservice.uniauth.modelvo.LoginUserInfo;
import com.synqnc.opengw.operate.service.bill.service.itf.BillDataListService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * BillDataListServiceImpl
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2025/8/6
 */
@Service
@Slf4j
public class BillDataListServiceImpl implements BillDataListService {

    @Autowired
    private RestTemplate restTemplate;

    @Value("${open-log-center.url:http://localhost:8081}")
    private String openLogCenterBaseUrl;

    private String getOpenLogCenterUrl() {
        return openLogCenterBaseUrl + "/bill/getBillDataList";
    }

    /**
     * 查询话单列表
     *
     * @param queryVO 查询条件
     * @param loginUserInfo 登录用户信息
     * @return 话单列表
     */
    @Override
    public PageUtils<BillDataListVO> getBillDataList(BillDataListQueryVO queryVO, LoginUserInfo loginUserInfo) {
        log.info("BillDataListServiceImpl.getBillDataList enter, queryVO={}, userId={}",
                JSON.toJSONString(queryVO), loginUserInfo.getId());
        
        try {
            // 构建请求参数
            Map<String, Object> requestBody = buildRequestBody(queryVO);
            
            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestBody, headers);
            
            String url = getOpenLogCenterUrl();
            log.info("BillDataListServiceImpl.getBillDataList calling open-log-center-service, url={}, requestBody={}",
                    url, JSON.toJSONString(requestBody));

            // 调用open-log-center-service
            @SuppressWarnings("rawtypes")
            ResponseEntity<Map> response = restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    Map.class
            );

            log.info("BillDataListServiceImpl.getBillDataList response from open-log-center-service, statusCode={}",
                    response.getStatusCode());

            // 处理响应
            @SuppressWarnings("unchecked")
            Map<String, Object> responseBody = response.getBody();
            if (responseBody == null) {
                log.error("BillDataListServiceImpl.getBillDataList response body is null");
                return new PageUtils<>(0L, queryVO.getPage(), queryVO.getPageSize(), new ArrayList<>());
            }
            
            Integer code = (Integer) responseBody.get("code");
            if (code == null || !code.equals(200)) {
                String msg = (String) responseBody.get("msg");
                log.error("BillDataListServiceImpl.getBillDataList error response, code={}, msg={}", code, msg);
                throw new RuntimeException("调用open-log-center-service失败：" + msg);
            }
            
            // 解析数据
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
            if (data == null) {
                log.warn("BillDataListServiceImpl.getBillDataList data is null");
                return new PageUtils<>(0L, queryVO.getPage(), queryVO.getPageSize(), new ArrayList<>());
            }

            Long totalCount = getLongValue(data.get("totalCount"));
            Integer pageSize = getIntegerValue(data.get("pageSize"));
            Long currentPage = getLongValue(data.get("currentPage"));
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> rows = (List<Map<String, Object>>) data.get("rows");
            
            // 转换数据
            List<BillDataListVO> billDataList = convertToBillDataListVO(rows);
            
            PageUtils<BillDataListVO> result = new PageUtils<>(totalCount, currentPage, pageSize, billDataList);
            
            log.info("BillDataListServiceImpl.getBillDataList exit, totalCount={}, resultSize={}", 
                    totalCount, billDataList.size());
            
            return result;
            
        } catch (Exception e) {
            log.error("BillDataListServiceImpl.getBillDataList error, queryVO={}", JSON.toJSONString(queryVO), e);
            throw new RuntimeException("查询话单列表失败", e);
        }
    }

    /**
     * 构建请求参数
     */
    private Map<String, Object> buildRequestBody(BillDataListQueryVO queryVO) {
        Map<String, Object> requestBody = new java.util.HashMap<>();
        
        if (queryVO.getPage() != null) {
            requestBody.put("page", queryVO.getPage());
        }
        if (queryVO.getPageSize() != null) {
            requestBody.put("pageSize", queryVO.getPageSize());
        }
        if (queryVO.getCapabilityName() != null) {
            requestBody.put("capabilityName", queryVO.getCapabilityName());
        }
        if (queryVO.getInterfaceName() != null) {
            requestBody.put("interfaceName", queryVO.getInterfaceName());
        }
        if (queryVO.getVersionNum() != null) {
            requestBody.put("versionNum", queryVO.getVersionNum());
        }
        if (queryVO.getInvokerId() != null) {
            requestBody.put("invokerId", queryVO.getInvokerId());
        }
        if (queryVO.getProvider() != null) {
            requestBody.put("provider", queryVO.getProvider());
        }
        if (queryVO.getOperatorId() != null) {
            requestBody.put("operatorId", queryVO.getOperatorId());
        }
        if (queryVO.getStatusCode() != null) {
            requestBody.put("statusCode", queryVO.getStatusCode());
        }
        if (queryVO.getBillGenerationTimeStart() != null) {
            requestBody.put("billGenerationTimeStart", queryVO.getBillGenerationTimeStart());
        }
        if (queryVO.getBillGenerationTimeEnd() != null) {
            requestBody.put("billGenerationTimeEnd", queryVO.getBillGenerationTimeEnd());
        }
        if (queryVO.getCallTimeStart() != null) {
            requestBody.put("callTimeStart", queryVO.getCallTimeStart());
        }
        if (queryVO.getCallTimeEnd() != null) {
            requestBody.put("callTimeEnd", queryVO.getCallTimeEnd());
        }
        if (queryVO.getBusinessOrderId() != null) {
            requestBody.put("businessOrderId", queryVO.getBusinessOrderId());
        }
        if (queryVO.getToBusinessOrderId() != null) {
            requestBody.put("toBusinessOrderId", queryVO.getToBusinessOrderId());
        }
        if (queryVO.getSourceIp() != null) {
            requestBody.put("sourceIp", queryVO.getSourceIp());
        }
        
        return requestBody;
    }

    /**
     * 转换数据为VO
     */
    private List<BillDataListVO> convertToBillDataListVO(List<Map<String, Object>> rows) {
        List<BillDataListVO> result = new ArrayList<>();
        
        if (rows == null || rows.isEmpty()) {
            return result;
        }
        
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        
        for (Map<String, Object> row : rows) {
            BillDataListVO vo = new BillDataListVO();
            vo.setBillId(getStringValue(row.get("billId")));
            vo.setBusinessOrderId(getStringValue(row.get("businessOrderId")));
            vo.setToBusinessOrderId(getStringValue(row.get("toBusinessOrderId")));
            vo.setCapabilityName(getStringValue(row.get("capabilityName")));
            vo.setCapabilityId(getStringValue(row.get("capabilityId")));
            vo.setInterfaceName(getStringValue(row.get("interfaceName")));
            vo.setInterfaceId(getStringValue(row.get("interfaceId")));
            vo.setVersionNum(getStringValue(row.get("versionNum")));
            vo.setCallTime(formatDate(row.get("callTime"), sdf));
            vo.setBillGenerationTime(formatDate(row.get("billGenerationTime"), sdf));
            vo.setInvokerId(getStringValue(row.get("invokerId")));
            vo.setAppId(getStringValue(row.get("appId")));
            vo.setOperatorId(getStringValue(row.get("operatorId")));
            vo.setProvider(getStringValue(row.get("provider")));
            vo.setSourceIp(getStringValue(row.get("sourceIp")));
            vo.setStatus(getStringValue(row.get("status")));
            vo.setStatusCode(getIntegerValue(row.get("statusCode")));
            vo.setBusinessId(getStringValue(row.get("businessId")));
            vo.setGatewayId(getStringValue(row.get("gatewayId")));
            vo.setSessionStatus(getIntegerValue(row.get("sessionStatus")));
            vo.setBillNumber(getStringValue(row.get("billNumber")));
            vo.setSessionStarttime(formatDate(row.get("sessionStarttime"), sdf));
            vo.setSessionEndtime(formatDate(row.get("sessionEndtime"), sdf));
            vo.setSessionDuration(getIntegerValue(row.get("sessionDuration")));
            vo.setPhoneNumber(getStringValue(row.get("phoneNumber")));
            vo.setIpv4Address(getStringValue(row.get("ipv4Address")));
            vo.setIpv6Address(getStringValue(row.get("ipv6Address")));
            vo.setNetworkAccessIdentifier(getStringValue(row.get("networkAccessIdentifier")));
            vo.setFeatureParam(getStringValue(row.get("featureParam")));
            vo.setBillTemplateId(getStringValue(row.get("billTemplateId")));
            vo.setBillTemplateVersion(getStringValue(row.get("billTemplateVersion")));
            vo.setReservedField01(getStringValue(row.get("reservedField01")));
            vo.setReservedField02(getStringValue(row.get("reservedField02")));
            
            result.add(vo);
        }
        
        return result;
    }

    private String getStringValue(Object value) {
        return value != null ? value.toString() : null;
    }

    private Integer getIntegerValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        try {
            return Integer.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Long getLongValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Integer) {
            return ((Integer) value).longValue();
        }
        try {
            return Long.valueOf(value.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private String formatDate(Object dateValue, SimpleDateFormat sdf) {
        if (dateValue == null) {
            return null;
        }
        if (dateValue instanceof Date) {
            return sdf.format((Date) dateValue);
        }
        if (dateValue instanceof String) {
            return (String) dateValue;
        }
        return dateValue.toString();
    }
}
