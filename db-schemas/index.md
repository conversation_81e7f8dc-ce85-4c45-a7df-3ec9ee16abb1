## 表名：bill_data_distributed 
### DDL:
```
-- opengateway_test.bill_data_distributed definition

CREATE TABLE opengateway_test.bill_data_distributed
(

    `bill_id` String COMMENT '话单编号，为每一条话单分配的一个唯一标识符',

    `business_order_id` String COMMENT '业支业务号码，示例：B0100182470S，订单同步过来的',

    `to_business_order_id` String COMMENT 'tob业务标识,
订单同步过来的',

    `capability_name` String COMMENT '能力名称，对应OGW平台能力名称',

    `capability_id` String COMMENT '能力标识，对应OGW平台能力标识，辅助数据有值',

    `interface_name` String COMMENT '接口名称，对应OGW平台接口名称',

    `interface_id` String COMMENT '接口标识，对应OGW平台接口标识，辅助数据有值',

    `version_num` String COMMENT '版本号，对应OGW平台版本号，辅助数据有值',

    `call_time` DateTime64(3,
 'Asia/Shanghai') COMMENT '调用时间，由OGW调用日志生成',

    `bill_generation_time` DateTime64(3,
 'Asia/Shanghai') COMMENT '话单生成时间，OGW平台生成话单的时间',

    `invoker_id` String COMMENT '调用方标识（企业标识）',

    `app_id` String COMMENT '调用应用id',

    `operator_id` String COMMENT '能力所属运营商标识，OGW平台根据路由查询得到',

    `provider` String COMMENT 'API提供方标识，和平台直连的转接方或者平台（能力归属运营商）',

    `source_ip` String COMMENT '调用方来源IP，请求消息对应的源IP',

    `status` String COMMENT '调用状态：成功，失败',

    `business_id` String COMMENT '会话ID',

    `gateway_id` String COMMENT '能力网关标识，应用调用的触点网关节点',

    `session_status` Int8 COMMENT '是否有后续话单，否(0)、是(1)',

    `bill_number` String COMMENT '中间话单编号，0001-9999',

    `session_starttime` DateTime64(3,
 'Asia/Shanghai') COMMENT '会话开始时间，记录会话开始时间',

    `session_endtime` DateTime64(3,
 'Asia/Shanghai') COMMENT '会话结束时间，话单生成时，会话未结束，则该字段为空',

    `session_duration` Int16 COMMENT '会话持续时间，单位：秒；会话状态为进行中时，持续时间等于话单生成时间减去会话开始时间',

    `phone_number` String COMMENT '电话号码',

    `ipv4_address` String COMMENT '设备IPV4地址，需要加速的设备的公网ipv4地址+端口',

    `ipv6_address` String COMMENT '设备IPV6地址，需要加速的设备的公网ipv6地址',

    `network_access_identifier` String COMMENT '网络接入标识，网络接入信息',

    `feature_param` String COMMENT '能力特征参数1，比如QoS配置文件id，这里放个json，value是{feature_param1:value1,
feature_param2:value2}',

    `status_code` Int8 COMMENT '状态码，业务调用成功字段需要单独定义，东西向对接的需要考虑',

    `bill_template_id` String COMMENT '话单模版编号',

    `bill_template_version` String COMMENT '话单模版版本',

    `reserved_field_01` String COMMENT '话单预留字段1',

    `reserved_field_02` String COMMENT '话单预留字段2'
)
ENGINE = Distributed('hx_gm_data',
 'opengateway_test',
 'bill_data_local',
 rand());
```