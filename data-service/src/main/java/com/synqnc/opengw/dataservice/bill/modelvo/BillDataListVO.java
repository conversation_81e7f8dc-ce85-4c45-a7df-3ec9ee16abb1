package com.synqnc.opengw.dataservice.bill.modelvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * BillDataListVO
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2025/8/6
 */
@Data
@ApiModel(value = "话单列表VO", description = "话单列表响应数据")
public class BillDataListVO {

    @ApiModelProperty(value = "话单编号", example = "BILL_20250101_001")
    private String billId;

    @ApiModelProperty(value = "业支业务号码", example = "B0100182470S")
    private String businessOrderId;

    @ApiModelProperty(value = "政企业务编码", example = "TOB001")
    private String toBusinessOrderId;

    @ApiModelProperty(value = "能力名称", example = "短信发送")
    private String capabilityName;

    @ApiModelProperty(value = "能力标识", example = "SMS_SEND")
    private String capabilityId;

    @ApiModelProperty(value = "接口名称", example = "sendSms")
    private String interfaceName;

    @ApiModelProperty(value = "接口标识", example = "SMS_SEND_API")
    private String interfaceId;

    @ApiModelProperty(value = "版本号", example = "v1.0")
    private String versionNum;

    @ApiModelProperty(value = "调用时间", example = "2025-01-01 10:30:00")
    private String callTime;

    @ApiModelProperty(value = "话单生成时间", example = "2025-01-01 10:30:05")
    private String billGenerationTime;

    @ApiModelProperty(value = "调用方标识", example = "UNICOM")
    private String invokerId;

    @ApiModelProperty(value = "调用应用ID", example = "APP_001")
    private String appId;

    @ApiModelProperty(value = "能力所属运营商标识", example = "UNICOM_001")
    private String operatorId;

    @ApiModelProperty(value = "API提供方标识", example = "10001")
    private String provider;

    @ApiModelProperty(value = "调用方来源IP", example = "***********")
    private String sourceIp;

    @ApiModelProperty(value = "调用状态", example = "成功")
    private String status;

    @ApiModelProperty(value = "状态码", example = "200")
    private Integer statusCode;

    @ApiModelProperty(value = "业务ID", example = "BIZ_001")
    private String businessId;

    @ApiModelProperty(value = "能力网关标识", example = "GW_001")
    private String gatewayId;

    @ApiModelProperty(value = "是否有后续话单", example = "0")
    private Integer sessionStatus;

    @ApiModelProperty(value = "中间话单编号", example = "0001")
    private String billNumber;

    @ApiModelProperty(value = "会话开始时间", example = "2025-01-01 10:30:00")
    private String sessionStarttime;

    @ApiModelProperty(value = "会话结束时间", example = "2025-01-01 10:30:10")
    private String sessionEndtime;

    @ApiModelProperty(value = "会话持续时间", example = "10")
    private Integer sessionDuration;

    @ApiModelProperty(value = "电话号码", example = "***********")
    private String phoneNumber;

    @ApiModelProperty(value = "设备IPV4地址", example = "***********00:8080")
    private String ipv4Address;

    @ApiModelProperty(value = "设备IPV6地址", example = "2001:db8::1")
    private String ipv6Address;

    @ApiModelProperty(value = "网络接入标识", example = "NAI_001")
    private String networkAccessIdentifier;

    @ApiModelProperty(value = "能力特征参数", example = "{\"qos\":\"high\"}")
    private String featureParam;

    @ApiModelProperty(value = "话单模版编号", example = "TEMPLATE_001")
    private String billTemplateId;

    @ApiModelProperty(value = "话单模版版本", example = "v1.0")
    private String billTemplateVersion;

    @ApiModelProperty(value = "话单预留字段1", example = "reserved1")
    private String reservedField01;

    @ApiModelProperty(value = "话单预留字段2", example = "reserved2")
    private String reservedField02;
}
