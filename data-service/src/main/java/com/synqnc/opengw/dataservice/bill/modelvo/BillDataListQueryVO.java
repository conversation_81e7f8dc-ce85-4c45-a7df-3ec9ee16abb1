package com.synqnc.opengw.dataservice.bill.modelvo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * BillDataListQueryVO
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2025/8/6
 */
@Data
@ApiModel(value = "话单列表查询VO", description = "话单列表查询请求参数")
public class BillDataListQueryVO {

    @ApiModelProperty(value = "当前页数", example = "1")
    private Long page;

    @ApiModelProperty(value = "每页条数", example = "10")
    private Integer pageSize;

    @ApiModelProperty(value = "能力名称", example = "短信发送")
    private String capabilityName;

    @ApiModelProperty(value = "接口名称", example = "sendSms")
    private String interfaceName;

    @ApiModelProperty(value = "版本号", example = "v1.0")
    private String versionNum;

    @ApiModelProperty(value = "调用方(uni_organization表的orgkey)", example = "UNICOM")
    private String invokerId;

    @ApiModelProperty(value = "API提供方(邻接点索引号)", example = "10001")
    private String provider;

    @ApiModelProperty(value = "能力所属运营商(operatorId)", example = "UNICOM_001")
    private String operatorId;

    @ApiModelProperty(value = "状态码", example = "200")
    private Integer statusCode;

    @ApiModelProperty(value = "生成开始时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2025-01-01 00:00:00")
    private String billGenerationTimeStart;

    @ApiModelProperty(value = "生成结束时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2025-01-31 23:59:59")
    private String billGenerationTimeEnd;

    @ApiModelProperty(value = "调用开始时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2025-01-01 00:00:00")
    private String callTimeStart;

    @ApiModelProperty(value = "调用结束时间（格式：yyyy-MM-dd HH:mm:ss）", example = "2025-01-31 23:59:59")
    private String callTimeEnd;

    @ApiModelProperty(value = "业支业务号码（模糊查询）", example = "B0100182470S")
    private String businessOrderId;

    @ApiModelProperty(value = "政企业务编码（模糊查询）", example = "TOB001")
    private String toBusinessOrderId;

    @ApiModelProperty(value = "调用方来源IP（模糊查询）", example = "***********")
    private String sourceIp;
}
