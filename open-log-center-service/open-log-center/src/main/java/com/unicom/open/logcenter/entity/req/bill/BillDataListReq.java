package com.unicom.open.logcenter.entity.req.bill;

import com.unicom.open.logcenter.bean.resp.PageQuerySO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * BillDataListReq
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2025/8/6
 */
@Data
@ApiModel(value = "话单列表查询请求类")
public class BillDataListReq extends PageQuerySO {

    @ApiModelProperty(value = "能力名称")
    private String capabilityName;

    @ApiModelProperty(value = "接口名称")
    private String interfaceName;

    @ApiModelProperty(value = "版本号")
    private String versionNum;

    @ApiModelProperty(value = "调用方(uni_organization表的orgkey)")
    private String invokerId;

    @ApiModelProperty(value = "API提供方(邻接点索引号)")
    private String provider;

    @ApiModelProperty(value = "能力所属运营商(operatorId)")
    private String operatorId;

    @ApiModelProperty(value = "状态码")
    private Integer statusCode;

    @ApiModelProperty(value = "生成开始时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String billGenerationTimeStart;

    @ApiModelProperty(value = "生成结束时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String billGenerationTimeEnd;

    @ApiModelProperty(value = "调用开始时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String callTimeStart;

    @ApiModelProperty(value = "调用结束时间（格式：yyyy-MM-dd HH:mm:ss）")
    private String callTimeEnd;

    @ApiModelProperty(value = "业支业务号码（模糊查询）")
    private String businessOrderId;

    @ApiModelProperty(value = "政企业务编码（模糊查询）")
    private String toBusinessOrderId;

    @ApiModelProperty(value = "调用方来源IP（模糊查询）")
    private String sourceIp;
}
