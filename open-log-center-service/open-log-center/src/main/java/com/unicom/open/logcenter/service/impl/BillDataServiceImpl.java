package com.unicom.open.logcenter.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.unicom.open.logcenter.bean.resp.PageUtils;
import com.unicom.open.logcenter.entity.req.bill.BillDataListReq;
import com.unicom.open.logcenter.entity.resp.bill.BillDataListResp;
import com.unicom.open.logcenter.service.BillDataService;
import com.unicom.open.logcenter.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * BillDataServiceImpl
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2025/8/6
 */
@Service
@Slf4j
@DS("clickhouse")
public class BillDataServiceImpl implements BillDataService {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 查询话单列表
     *
     * @param req 请求参数
     * @return 话单列表
     */
    @Override
    public PageUtils<BillDataListResp> getBillDataList(BillDataListReq req) {
        log.info("BillDataServiceImpl.getBillDataList enter, req={}", req);
        
        try {
            // 构建查询SQL
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT ");
            sql.append("bill_id, business_order_id, to_business_order_id, capability_name, capability_id, ");
            sql.append("interface_name, interface_id, version_num, call_time, bill_generation_time, ");
            sql.append("invoker_id, app_id, operator_id, provider, source_ip, status, status_code, ");
            sql.append("business_id, gateway_id, session_status, bill_number, session_starttime, ");
            sql.append("session_endtime, session_duration, phone_number, ipv4_address, ipv6_address, ");
            sql.append("network_access_identifier, feature_param, bill_template_id, bill_template_version, ");
            sql.append("reserved_field_01, reserved_field_02 ");
            sql.append("FROM bill_data_distributed ");
            sql.append("WHERE 1=1 ");

            List<Object> params = new ArrayList<>();

            // 精确筛选条件
            if (StringUtils.hasText(req.getCapabilityName())) {
                sql.append("AND capability_name = ? ");
                params.add(req.getCapabilityName());
            }

            if (StringUtils.hasText(req.getInterfaceName())) {
                sql.append("AND interface_name = ? ");
                params.add(req.getInterfaceName());
            }

            if (StringUtils.hasText(req.getVersionNum())) {
                sql.append("AND version_num = ? ");
                params.add(req.getVersionNum());
            }

            if (StringUtils.hasText(req.getInvokerId())) {
                sql.append("AND invoker_id = ? ");
                params.add(req.getInvokerId());
            }

            if (StringUtils.hasText(req.getProvider())) {
                sql.append("AND provider = ? ");
                params.add(req.getProvider());
            }

            if (StringUtils.hasText(req.getOperatorId())) {
                sql.append("AND operator_id = ? ");
                params.add(req.getOperatorId());
            }

            if (req.getStatusCode() != null) {
                sql.append("AND status_code = ? ");
                params.add(req.getStatusCode());
            }

            // 时间范围筛选
            if (StringUtils.hasText(req.getBillGenerationTimeStart())) {
                sql.append("AND bill_generation_time >= ? ");
                params.add(DateUtils.parseDateTime(req.getBillGenerationTimeStart()));
            }

            if (StringUtils.hasText(req.getBillGenerationTimeEnd())) {
                sql.append("AND bill_generation_time <= ? ");
                params.add(DateUtils.parseDateTime(req.getBillGenerationTimeEnd()));
            }

            if (StringUtils.hasText(req.getCallTimeStart())) {
                sql.append("AND call_time >= ? ");
                params.add(DateUtils.parseDateTime(req.getCallTimeStart()));
            }

            if (StringUtils.hasText(req.getCallTimeEnd())) {
                sql.append("AND call_time <= ? ");
                params.add(DateUtils.parseDateTime(req.getCallTimeEnd()));
            }

            // 模糊筛选条件
            if (StringUtils.hasText(req.getBusinessOrderId())) {
                sql.append("AND business_order_id LIKE ? ");
                params.add("%" + req.getBusinessOrderId() + "%");
            }

            if (StringUtils.hasText(req.getToBusinessOrderId())) {
                sql.append("AND to_business_order_id LIKE ? ");
                params.add("%" + req.getToBusinessOrderId() + "%");
            }

            if (StringUtils.hasText(req.getSourceIp())) {
                sql.append("AND source_ip LIKE ? ");
                params.add("%" + req.getSourceIp() + "%");
            }

            // 按话单生成时间倒序
            sql.append("ORDER BY bill_generation_time DESC ");

            // 分页处理
            Long totalCount = getTotalCount(sql.toString(), params);
            
            if (req.getPageSize() != null && req.getPage() != null && req.getPageSize() > 0) {
                long offset = (req.getPage() - 1) * req.getPageSize();
                sql.append("LIMIT ").append(req.getPageSize()).append(" OFFSET ").append(offset);
            }

            log.info("BillDataServiceImpl.getBillDataList sql={}, params={}", sql.toString(), params);

            List<Map<String, Object>> resultList = jdbcTemplate.queryForList(sql.toString(), params.toArray());
            List<BillDataListResp> billDataList = convertToBillDataList(resultList);

            PageUtils<BillDataListResp> pageUtils = new PageUtils<>(totalCount, req.getPage(), req.getPageSize(), billDataList);
            
            log.info("BillDataServiceImpl.getBillDataList exit, totalCount={}, resultSize={}", totalCount, billDataList.size());
            return pageUtils;
            
        } catch (Exception e) {
            log.error("BillDataServiceImpl.getBillDataList error, req={}", req, e);
            throw new RuntimeException("查询话单列表失败", e);
        }
    }

    /**
     * 获取总数
     */
    private Long getTotalCount(String sql, List<Object> params) {
        String countSql = sql.replace("SELECT bill_id, business_order_id, to_business_order_id, capability_name, capability_id, interface_name, interface_id, version_num, call_time, bill_generation_time, invoker_id, app_id, operator_id, provider, source_ip, status, status_code, business_id, gateway_id, session_status, bill_number, session_starttime, session_endtime, session_duration, phone_number, ipv4_address, ipv6_address, network_access_identifier, feature_param, bill_template_id, bill_template_version, reserved_field_01, reserved_field_02 FROM", "SELECT COUNT(*) FROM");
        
        // 移除ORDER BY子句
        int orderByIndex = countSql.indexOf("ORDER BY");
        if (orderByIndex > 0) {
            countSql = countSql.substring(0, orderByIndex);
        }
        
        return jdbcTemplate.queryForObject(countSql, params.toArray(), Long.class);
    }

    /**
     * 转换查询结果为响应对象
     */
    private List<BillDataListResp> convertToBillDataList(List<Map<String, Object>> resultList) {
        List<BillDataListResp> billDataList = new ArrayList<>();
        
        for (Map<String, Object> row : resultList) {
            BillDataListResp resp = new BillDataListResp();
            resp.setBillId((String) row.get("bill_id"));
            resp.setBusinessOrderId((String) row.get("business_order_id"));
            resp.setToBusinessOrderId((String) row.get("to_business_order_id"));
            resp.setCapabilityName((String) row.get("capability_name"));
            resp.setCapabilityId((String) row.get("capability_id"));
            resp.setInterfaceName((String) row.get("interface_name"));
            resp.setInterfaceId((String) row.get("interface_id"));
            resp.setVersionNum((String) row.get("version_num"));
            resp.setCallTime((java.util.Date) row.get("call_time"));
            resp.setBillGenerationTime((java.util.Date) row.get("bill_generation_time"));
            resp.setInvokerId((String) row.get("invoker_id"));
            resp.setAppId((String) row.get("app_id"));
            resp.setOperatorId((String) row.get("operator_id"));
            resp.setProvider((String) row.get("provider"));
            resp.setSourceIp((String) row.get("source_ip"));
            resp.setStatus((String) row.get("status"));
            resp.setStatusCode((Integer) row.get("status_code"));
            resp.setBusinessId((String) row.get("business_id"));
            resp.setGatewayId((String) row.get("gateway_id"));
            resp.setSessionStatus((Integer) row.get("session_status"));
            resp.setBillNumber((String) row.get("bill_number"));
            resp.setSessionStarttime((java.util.Date) row.get("session_starttime"));
            resp.setSessionEndtime((java.util.Date) row.get("session_endtime"));
            resp.setSessionDuration((Integer) row.get("session_duration"));
            resp.setPhoneNumber((String) row.get("phone_number"));
            resp.setIpv4Address((String) row.get("ipv4_address"));
            resp.setIpv6Address((String) row.get("ipv6_address"));
            resp.setNetworkAccessIdentifier((String) row.get("network_access_identifier"));
            resp.setFeatureParam((String) row.get("feature_param"));
            resp.setBillTemplateId((String) row.get("bill_template_id"));
            resp.setBillTemplateVersion((String) row.get("bill_template_version"));
            resp.setReservedField01((String) row.get("reserved_field_01"));
            resp.setReservedField02((String) row.get("reserved_field_02"));
            
            billDataList.add(resp);
        }
        
        return billDataList;
    }
}
