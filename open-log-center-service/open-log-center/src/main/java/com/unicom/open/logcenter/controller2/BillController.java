package com.unicom.open.logcenter.controller2;

import com.alibaba.fastjson.JSON;
import com.unicom.open.logcenter.bean.resp.BaseResp;
import com.unicom.open.logcenter.bean.resp.PageUtils;
import com.unicom.open.logcenter.entity.req.bill.BillDataListReq;
import com.unicom.open.logcenter.entity.resp.bill.BillDataListResp;
import com.unicom.open.logcenter.service.BillDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * BillController
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2025/8/6
 */
@RestController
@Slf4j
@Api(tags = "话单查询ck接口")
@RequestMapping("/bill")
public class BillController {

    @Resource
    private BillDataService billDataService;

    /**
     * 查询话单列表
     *
     * @param req 请求参数
     * @return 话单列表
     */
    @ApiOperation(value = "查询话单列表", notes = "根据条件查询话单列表，按话单生成时间倒序")
    @PostMapping("/getBillDataList")
    public BaseResp<PageUtils<BillDataListResp>> getBillDataList(@RequestBody BillDataListReq req) {
        log.info("BillController.getBillDataList enter, req={}", JSON.toJSONString(req));

        try {
            PageUtils<BillDataListResp> result = billDataService.getBillDataList(req);
            log.info("BillController.getBillDataList exit, totalCount={}", result.getTotalCount());
            return BaseResp.ofSuccess(result);
        } catch (Exception e) {
            log.error("BillController.getBillDataList error, req={}", JSON.toJSONString(req), e);
            return BaseResp.ofFail("查询话单列表失败：" + e.getMessage());
        }
    }
}
