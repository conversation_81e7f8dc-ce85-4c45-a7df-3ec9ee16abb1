package com.unicom.open.logcenter.entity.resp.bill;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * BillDataListResp
 *
 * <AUTHOR> Assistant
 * @version V1.0.0
 * @date 2025/8/6
 */
@Data
@ApiModel(value = "话单列表响应类")
public class BillDataListResp {

    @ApiModelProperty(value = "话单编号")
    private String billId;

    @ApiModelProperty(value = "业支业务号码")
    private String businessOrderId;

    @ApiModelProperty(value = "政企业务编码")
    private String toBusinessOrderId;

    @ApiModelProperty(value = "能力名称")
    private String capabilityName;

    @ApiModelProperty(value = "能力标识")
    private String capabilityId;

    @ApiModelProperty(value = "接口名称")
    private String interfaceName;

    @ApiModelProperty(value = "接口标识")
    private String interfaceId;

    @ApiModelProperty(value = "版本号")
    private String versionNum;

    @ApiModelProperty(value = "调用时间")
    private Date callTime;

    @ApiModelProperty(value = "话单生成时间")
    private Date billGenerationTime;

    @ApiModelProperty(value = "调用方标识")
    private String invokerId;

    @ApiModelProperty(value = "调用应用ID")
    private String appId;

    @ApiModelProperty(value = "能力所属运营商标识")
    private String operatorId;

    @ApiModelProperty(value = "API提供方标识")
    private String provider;

    @ApiModelProperty(value = "调用方来源IP")
    private String sourceIp;

    @ApiModelProperty(value = "调用状态")
    private String status;

    @ApiModelProperty(value = "状态码")
    private Integer statusCode;

    @ApiModelProperty(value = "业务ID")
    private String businessId;

    @ApiModelProperty(value = "能力网关标识")
    private String gatewayId;

    @ApiModelProperty(value = "是否有后续话单")
    private Integer sessionStatus;

    @ApiModelProperty(value = "中间话单编号")
    private String billNumber;

    @ApiModelProperty(value = "会话开始时间")
    private Date sessionStarttime;

    @ApiModelProperty(value = "会话结束时间")
    private Date sessionEndtime;

    @ApiModelProperty(value = "会话持续时间")
    private Integer sessionDuration;

    @ApiModelProperty(value = "电话号码")
    private String phoneNumber;

    @ApiModelProperty(value = "设备IPV4地址")
    private String ipv4Address;

    @ApiModelProperty(value = "设备IPV6地址")
    private String ipv6Address;

    @ApiModelProperty(value = "网络接入标识")
    private String networkAccessIdentifier;

    @ApiModelProperty(value = "能力特征参数")
    private String featureParam;

    @ApiModelProperty(value = "话单模版编号")
    private String billTemplateId;

    @ApiModelProperty(value = "话单模版版本")
    private String billTemplateVersion;

    @ApiModelProperty(value = "话单预留字段1")
    private String reservedField01;

    @ApiModelProperty(value = "话单预留字段2")
    private String reservedField02;
}
